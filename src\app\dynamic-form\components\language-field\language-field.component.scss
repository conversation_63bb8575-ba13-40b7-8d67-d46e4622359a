/* Language Field Component - EXACT match to existing form field styles */

/* Import existing form field styles - EXACT from regular-field.component.scss */
.form-field {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
}

.form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

/* Required field indicator styling - EXACT match */
.form-field label span {
  color: #dc3545;
  font-weight: bold;
  margin-left: 2px;
}

/* No Input Indicator Styling - EXACT match */
.no-input-indicator {
  color: #e74c3c;
  font-size: 11px;
  font-weight: normal;
  font-style: italic;
}

/* Form Input - EXACT match to existing styles */
.form-input:not([type='checkbox']) {
  flex-grow: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 8px 12px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
}

/* Disabled/readonly styles - EXACT match */
.form-input[disabled],
.form-input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Language-specific styles */
.language-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.language-main-input {
  padding-right: 45px !important; /* Make room for globe icon */
}

.language-globe-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
    color: #2196f3;
  }

  &:focus {
    outline: none;
    background-color: rgba(33, 150, 243, 0.1);
  }

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

/* Language Popup Styles */
.language-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.2s ease-in-out;
}

.language-popup {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;

  h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .close-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: #666;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
      color: #333;
    }

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }
}

.popup-content {
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
}

.popup-field {
  margin-bottom: 20px;

  &.default-field {
    .popup-field-label {
      color: #2196f3;
      font-weight: 600;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.popup-field-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 14px;
  color: #333;

  .default-indicator {
    font-size: 12px;
    color: #2196f3;
    font-weight: normal;
    margin-left: 8px;
  }
}

.popup-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Poppins', sans-serif;
  transition: all 0.2s ease;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: #2196f3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
  }

  &:hover:not(:disabled):not([readonly]) {
    border-color: #bbb;
  }

  &::placeholder {
    color: #999;
    font-style: italic;
  }
}

.popup-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;

  .save-btn,
  .cancel-btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
  }

  .save-btn {
    background-color: #2196f3;
    color: white;

    &:hover {
      background-color: #1976d2;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.3);
    }
  }

  .cancel-btn {
    background-color: #f5f5f5;
    color: #666;

    &:hover {
      background-color: #e0e0e0;
      color: #333;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
    }
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-input:not([type='checkbox']) {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 10px 12px;
  }

  .language-main-input {
    padding-right: 40px !important;
  }

  .language-globe-btn {
    right: 6px;
    width: 28px;
    height: 28px;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }

  .language-popup {
    width: 95%;
    max-height: 90vh;
  }

  .popup-header,
  .popup-footer {
    padding: 16px 20px;
  }

  .popup-content {
    padding: 20px;
  }

  .popup-footer {
    flex-direction: column;
    gap: 8px;

    .save-btn,
    .cancel-btn {
      width: 100%;
      padding: 12px 20px;
    }
  }
}

@media (max-width: 480px) {
  .form-input:not([type='checkbox']) {
    padding: 8px 10px;
    font-size: 14px;
  }

  .language-main-input {
    padding-right: 35px !important;
  }

  .language-globe-btn {
    right: 4px;
    width: 24px;
    height: 24px;

    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }
  }

  .popup-header h4 {
    font-size: 16px;
  }

  .popup-field-label {
    font-size: 13px;
  }

  .popup-input {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* No Input Indicator Responsive - EXACT match */
@media (max-width: 768px) {
  .no-input-indicator {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .no-input-indicator {
    font-size: 9px;
  }
}

/* Animation for language switching */
.language-input {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0.7;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
