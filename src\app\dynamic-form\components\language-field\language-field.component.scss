/* Language Field Component Styles */
.language-field-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  margin-bottom: 16px;

  .field-label {
    font-weight: 500;
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
    display: block;

    .required-indicator {
      color: #f44336;
      margin-left: 2px;
    }

    .no-input-indicator {
      font-size: 12px;
      color: #666;
      font-weight: normal;
      font-style: italic;
    }
  }

  .input-container {
    display: flex;
    align-items: stretch;
    gap: 8px;
    position: relative;

    .input-wrapper {
      flex: 1;
      position: relative;

      .language-input {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        transition: all 0.2s ease;
        background-color: #fff;

        &:focus {
          outline: none;
          border-color: #2196f3;
          box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
        }

        &:hover:not(:disabled):not([readonly]) {
          border-color: #bbb;
        }

        &:disabled,
        &[readonly] {
          background-color: #f5f5f5;
          color: #666;
          cursor: not-allowed;
        }

        &.text-input {
          height: 44px;
        }

        &.textarea-input {
          min-height: 80px;
          resize: vertical;
          font-family: inherit;
        }

        &::placeholder {
          color: #999;
          font-style: italic;
        }

        // RTL support for Arabic
        &[dir="rtl"] {
          text-align: right;
          direction: rtl;
        }
      }
    }

    .language-toggle-btn {
      flex-shrink: 0;
      width: 44px;
      height: 44px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #fff;
      color: #666;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: #f5f5f5;
        border-color: #2196f3;
        color: #2196f3;
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
      }

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }

  .language-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #666;
    margin-top: 4px;

    .current-language {
      font-weight: 500;
      color: #2196f3;
    }

    .language-count {
      color: #999;
    }
  }

  .error-messages {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 4px;

    .error-message {
      font-size: 12px;
      color: #f44336;
      display: flex;
      align-items: center;
      gap: 4px;

      &::before {
        content: "⚠";
        font-size: 10px;
      }
    }
  }

  .debug-info {
    margin-top: 8px;
    padding: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    font-family: monospace;
    font-size: 11px;
    color: #666;
    border-left: 3px solid #2196f3;
  }

  // Responsive design
  @media (max-width: 768px) {
    .input-container {
      flex-direction: column;
      gap: 8px;

      .language-toggle-btn {
        align-self: flex-start;
        width: auto;
        padding: 8px 16px;
        height: auto;
      }
    }
  }

  // Dark theme support (if needed)
  &.dark-theme {
    .field-label {
      color: #e0e0e0;
    }

    .language-input {
      background-color: #424242;
      border-color: #666;
      color: #e0e0e0;

      &:focus {
        border-color: #64b5f6;
        box-shadow: 0 0 0 2px rgba(100, 181, 246, 0.1);
      }

      &:disabled,
      &[readonly] {
        background-color: #303030;
        color: #999;
      }

      &::placeholder {
        color: #999;
      }
    }

    .language-toggle-btn {
      background-color: #424242;
      border-color: #666;
      color: #e0e0e0;

      &:hover {
        background-color: #505050;
        border-color: #64b5f6;
        color: #64b5f6;
      }
    }

    .language-indicator {
      .current-language {
        color: #64b5f6;
      }
    }
  }

  // Animation for language switching
  .language-input {
    animation: fadeIn 0.2s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0.7;
      transform: translateY(-2px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
