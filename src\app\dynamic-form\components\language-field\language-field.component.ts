import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, inject } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AuthenticationService } from '../../../services/authentication.service';

@Component({
  selector: 'app-language-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule
  ],
  templateUrl: './language-field.component.html',
  styleUrl: './language-field.component.scss'
})
export class LanguageFieldComponent implements OnInit, OnDestroy {
  @Input() field!: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  
  @Output() fieldValueChange = new EventEmitter<{fieldName: string, value: any}>();

  private authService = inject(AuthenticationService);
  
  // Language management
  availableLanguages: string[] = [];
  currentLanguageIndex: number = 0;
  languageControls: { [key: string]: FormControl } = {};
  showLanguageSelector: boolean = false;

  ngOnInit(): void {
    this.initializeLanguageField();
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  private initializeLanguageField(): void {
    // Get available languages from authentication service
    this.availableLanguages = this.authService.getUserLanguages();
    
    // Create form controls for each language
    this.createLanguageControls();
    
    // Set up the main form control
    this.setupMainFormControl();
    
    // Show language selector only if multiple languages available
    this.showLanguageSelector = this.availableLanguages.length > 1;
  }

  private createLanguageControls(): void {
    const validators = this.field.mandatory ? [Validators.required] : [];
    
    this.availableLanguages.forEach(lang => {
      const controlName = `${this.field.fieldName}_${lang}`;
      this.languageControls[lang] = new FormControl('', validators);
      
      // Add language-specific controls to the form
      this.form.addControl(controlName, this.languageControls[lang]);
      
      // Disable if in view mode or field has noInput
      if (this.isViewMode || this.field.noInput) {
        this.languageControls[lang].disable({ emitEvent: false });
      }
    });
  }

  private setupMainFormControl(): void {
    // Ensure main form control exists
    if (!this.form.get(this.field.fieldName)) {
      const validators = this.field.mandatory ? [Validators.required] : [];
      const mainControl = new FormControl('', validators);
      this.form.addControl(this.field.fieldName, mainControl);
      
      // Disable if in view mode or field has noInput
      if (this.isViewMode || this.field.noInput) {
        mainControl.disable({ emitEvent: false });
      }
    }
    
    // Set initial value from first language
    this.updateMainControlFromCurrentLanguage();
  }

  toggleLanguage(): void {
    if (this.availableLanguages.length <= 1) return;
    
    this.currentLanguageIndex = (this.currentLanguageIndex + 1) % this.availableLanguages.length;
    this.updateMainControlFromCurrentLanguage();
  }

  private updateMainControlFromCurrentLanguage(): void {
    const currentLang = this.availableLanguages[this.currentLanguageIndex];
    const currentLangControl = this.languageControls[currentLang];
    const mainControl = this.form.get(this.field.fieldName);
    
    if (currentLangControl && mainControl) {
      // Update main control with current language value
      mainControl.setValue(currentLangControl.value, { emitEvent: false });
    }
  }

  onLanguageInputChange(language: string, value: any): void {
    // Update the language-specific control
    if (this.languageControls[language]) {
      this.languageControls[language].setValue(value, { emitEvent: false });
    }
    
    // If this is the current language, update the main control
    if (language === this.availableLanguages[this.currentLanguageIndex]) {
      const mainControl = this.form.get(this.field.fieldName);
      if (mainControl) {
        mainControl.setValue(value, { emitEvent: false });
      }
    }
    
    // Emit field value change
    this.fieldValueChange.emit({
      fieldName: this.field.fieldName,
      value: value
    });
  }

  getCurrentLanguage(): string {
    return this.availableLanguages[this.currentLanguageIndex] || 'en';
  }

  getCurrentLanguageControl(): FormControl {
    const currentLang = this.getCurrentLanguage();
    return this.languageControls[currentLang];
  }

  getLanguageDisplayName(langCode: string): string {
    const languageNames: { [key: string]: string } = {
      'en': 'English',
      'ar': 'العربية',
      'fr': 'Français',
      'es': 'Español',
      'de': 'Deutsch'
    };
    return languageNames[langCode] || langCode.toUpperCase();
  }

  getUniqueFieldId(): string {
    const currentLang = this.getCurrentLanguage();
    return `${this.field.fieldName}_${currentLang}_input`;
  }

  // Method to populate language fields with data
  populateLanguageFields(data: any): void {
    this.availableLanguages.forEach(lang => {
      const langFieldName = `${this.field.fieldName}_${lang}`;
      if (data[langFieldName] !== undefined && this.languageControls[lang]) {
        this.languageControls[lang].setValue(data[langFieldName], { emitEvent: false });
      }
    });
    
    // Update main control with first language value
    this.updateMainControlFromCurrentLanguage();
  }

  // Method to get all language field values
  getLanguageFieldValues(): { [key: string]: any } {
    const values: { [key: string]: any } = {};
    this.availableLanguages.forEach(lang => {
      const control = this.languageControls[lang];
      if (control) {
        values[`${this.field.fieldName}_${lang}`] = control.value;
      }
    });
    return values;
  }
}
