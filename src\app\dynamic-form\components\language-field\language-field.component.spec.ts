import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormGroup, FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { LanguageFieldComponent } from './language-field.component';
import { AuthenticationService } from '../../../services/authentication.service';

describe('LanguageFieldComponent', () => {
  let component: LanguageFieldComponent;
  let fixture: ComponentFixture<LanguageFieldComponent>;
  let mockAuthService: jasmine.SpyObj<AuthenticationService>;

  beforeEach(async () => {
    // Create mock authentication service
    mockAuthService = jasmine.createSpyObj('AuthenticationService', ['getUserLanguages']);
    mockAuthService.getUserLanguages.and.returnValue(['en', 'ar']);

    await TestBed.configureTestingModule({
      imports: [
        LanguageFieldComponent,
        ReactiveFormsModule,
        MatIconModule,
        MatButtonModule,
        MatTooltipModule,
        NoopAnimationsModule
      ],
      providers: [
        { provide: AuthenticationService, useValue: mockAuthService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LanguageFieldComponent);
    component = fixture.componentInstance;
    
    // Set up required inputs
    component.field = {
      fieldName: 'testField',
      label: 'Test Field',
      type: 'string',
      mandatory: false,
      language: true
    };
    component.form = new FormGroup({});
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with available languages', () => {
    expect(component.availableLanguages).toEqual(['en', 'ar']);
    expect(component.showLanguageSelector).toBe(true);
  });

  it('should create language-specific form controls', () => {
    expect(component.form.get('testField_en')).toBeTruthy();
    expect(component.form.get('testField_ar')).toBeTruthy();
    expect(component.form.get('testField')).toBeTruthy();
  });

  it('should toggle between languages', () => {
    expect(component.currentLanguageIndex).toBe(0);
    expect(component.getCurrentLanguage()).toBe('en');
    
    component.toggleLanguage();
    
    expect(component.currentLanguageIndex).toBe(1);
    expect(component.getCurrentLanguage()).toBe('ar');
    
    component.toggleLanguage();
    
    expect(component.currentLanguageIndex).toBe(0);
    expect(component.getCurrentLanguage()).toBe('en');
  });

  it('should update main control when language input changes', () => {
    const testValue = 'test value';
    
    component.onLanguageInputChange('en', testValue);
    
    expect(component.languageControls['en'].value).toBe(testValue);
    expect(component.form.get('testField')?.value).toBe(testValue);
  });

  it('should emit field value change', () => {
    spyOn(component.fieldValueChange, 'emit');
    const testValue = 'test value';
    
    component.onLanguageInputChange('en', testValue);
    
    expect(component.fieldValueChange.emit).toHaveBeenCalledWith({
      fieldName: 'testField',
      value: testValue
    });
  });

  it('should handle single language scenario', () => {
    mockAuthService.getUserLanguages.and.returnValue(['en']);
    component.ngOnInit();
    
    expect(component.showLanguageSelector).toBe(false);
    expect(component.availableLanguages).toEqual(['en']);
  });

  it('should disable controls in view mode', () => {
    component.isViewMode = true;
    component.ngOnInit();
    
    expect(component.languageControls['en'].disabled).toBe(true);
    expect(component.languageControls['ar'].disabled).toBe(true);
    expect(component.form.get('testField')?.disabled).toBe(true);
  });

  it('should disable controls when field has noInput', () => {
    component.field.noInput = true;
    component.ngOnInit();
    
    expect(component.languageControls['en'].disabled).toBe(true);
    expect(component.languageControls['ar'].disabled).toBe(true);
    expect(component.form.get('testField')?.disabled).toBe(true);
  });

  it('should populate language fields with data', () => {
    const testData = {
      'testField_en': 'English value',
      'testField_ar': 'Arabic value'
    };
    
    component.populateLanguageFields(testData);
    
    expect(component.languageControls['en'].value).toBe('English value');
    expect(component.languageControls['ar'].value).toBe('Arabic value');
  });

  it('should get all language field values', () => {
    component.languageControls['en'].setValue('English value');
    component.languageControls['ar'].setValue('Arabic value');
    
    const values = component.getLanguageFieldValues();
    
    expect(values).toEqual({
      'testField_en': 'English value',
      'testField_ar': 'Arabic value'
    });
  });

  it('should return correct language display names', () => {
    expect(component.getLanguageDisplayName('en')).toBe('English');
    expect(component.getLanguageDisplayName('ar')).toBe('العربية');
    expect(component.getLanguageDisplayName('unknown')).toBe('UNKNOWN');
  });

  it('should generate unique field IDs', () => {
    const fieldId = component.getUniqueFieldId();
    expect(fieldId).toBe('testField_en_input');
    
    component.toggleLanguage();
    const fieldId2 = component.getUniqueFieldId();
    expect(fieldId2).toBe('testField_ar_input');
  });
});
