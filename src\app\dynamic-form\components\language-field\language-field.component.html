<!-- Language Field Component - Matches existing field styles with popup -->
<div class="form-field" [formGroup]="form">

  <!-- Field Label - EXACT match to existing style -->
  <label [for]="getUniqueFieldId()">
    {{ field.label || field.fieldName }}
    @if (field.mandatory) {
      <span>*</span>
    }
    @if (field.noInput) {
      <span class="no-input-indicator"> (Read Only)</span>
    }
  </label>

  <!-- Input Container with Globe Icon Inside -->
  <div class="language-input-container">

    <!-- Main Input Field - EXACT match to existing form-input style -->
    @if (field.type === 'textarea') {
      <textarea
        [id]="getUniqueFieldId()"
        [formControl]="getMainFormControl()"
        [placeholder]="field.placeholder || (field.label?.trim() || field.fieldName)"
        [readonly]="isViewMode || field.noInput"
        [disabled]="isViewMode || field.noInput"
        class="form-input language-main-input"
        rows="3">
      </textarea>
    } @else {
      <input
        [id]="getUniqueFieldId()"
        [type]="getInputType()"
        [formControl]="getMainFormControl()"
        [placeholder]="field.placeholder || (field.label?.trim() || field.fieldName)"
        [readonly]="isViewMode || field.noInput"
        [disabled]="isViewMode || field.noInput"
        class="form-input language-main-input">
    }

    <!-- Globe Icon Inside Field (only show if multiple languages) -->
    @if (showLanguageSelector && !isViewMode && !field.noInput) {
      <button
        type="button"
        class="language-globe-btn"
        [matTooltip]="'Edit in multiple languages'"
        (click)="toggleLanguagePopup($event)">
        <mat-icon>language</mat-icon>
      </button>
    }
  </div>

  <!-- Language Popup -->
  @if (showLanguagePopup) {
    <div class="language-popup-overlay" (click)="closeLanguagePopup()">
      <div class="language-popup" (click)="$event.stopPropagation()">

        <!-- Popup Header -->
        <div class="popup-header">
          <h4>Edit in Multiple Languages</h4>
          <button type="button" class="close-btn" (click)="closeLanguagePopup()">
            <mat-icon>close</mat-icon>
          </button>
        </div>

        <!-- Language Fields -->
        <div class="popup-content">
          @for (language of availableLanguages; track language; let i = $index) {
            <div class="popup-field" [class.default-field]="i === 0">

              <!-- Language Label -->
              <label class="popup-field-label">
                {{ getLanguageDisplayName(language) }}
                @if (i === 0) {
                  <span class="default-indicator">(Default)</span>
                }
              </label>

              <!-- Language Input -->
              @if (field.type === 'textarea') {
                <textarea
                  [formControl]="languageControls[language]"
                  [placeholder]="'Enter ' + (field.label || field.fieldName) + ' in ' + getLanguageDisplayName(language)"
                  class="popup-input"
                  rows="3"
                  (input)="onLanguageInputChange(language, $any($event.target).value)">
                </textarea>
              } @else {
                <input
                  [type]="getInputType()"
                  [formControl]="languageControls[language]"
                  [placeholder]="'Enter ' + (field.label || field.fieldName) + ' in ' + getLanguageDisplayName(language)"
                  class="popup-input"
                  (input)="onLanguageInputChange(language, $any($event.target).value)">
              }
            </div>
          }
        </div>

        <!-- Popup Footer -->
        <div class="popup-footer">
          <button type="button" class="save-btn" (click)="saveLanguageChanges()">
            Save Changes
          </button>
          <button type="button" class="cancel-btn" (click)="closeLanguagePopup()">
            Cancel
          </button>
        </div>

      </div>
    </div>
  }

</div>
