<!-- Language Field Component - Multi-language input with globe icon -->
<div class="language-field-container" [formGroup]="form">
  
  <!-- Field Label -->
  <label [for]="getUniqueFieldId()" class="field-label">
    {{ field.label || field.fieldName }}
    @if (field.mandatory) {
      <span class="required-indicator">*</span>
    }
    @if (field.noInput) {
      <span class="no-input-indicator"> (Read Only)</span>
    }
  </label>

  <!-- Input Container with Globe Icon -->
  <div class="input-container">
    
    <!-- Language Input Field -->
    <div class="input-wrapper">
      @if (field.type === 'textarea') {
        <textarea
          [id]="getUniqueFieldId()"
          [formControl]="getCurrentLanguageControl()"
          [placeholder]="field.placeholder || 'Enter ' + (field.label || field.fieldName)"
          [readonly]="isViewMode || field.noInput"
          [disabled]="isViewMode || field.noInput"
          class="language-input textarea-input"
          rows="3"
          (input)="onLanguageInputChange(getCurrentLanguage(), $any($event.target).value)">
        </textarea>
      } @else {
        <input
          [id]="getUniqueFieldId()"
          [type]="field.type === 'password' ? 'password' : 'text'"
          [formControl]="getCurrentLanguageControl()"
          [placeholder]="field.placeholder || 'Enter ' + (field.label || field.fieldName)"
          [readonly]="isViewMode || field.noInput"
          [disabled]="isViewMode || field.noInput"
          class="language-input text-input"
          (input)="onLanguageInputChange(getCurrentLanguage(), $any($event.target).value)">
      }
    </div>

    <!-- Globe Icon Button (only show if multiple languages) -->
    @if (showLanguageSelector && !isViewMode && !field.noInput) {
      <button
        type="button"
        mat-icon-button
        class="language-toggle-btn"
        [matTooltip]="'Switch to ' + getLanguageDisplayName(availableLanguages[(currentLanguageIndex + 1) % availableLanguages.length])"
        (click)="toggleLanguage()">
        <mat-icon>language</mat-icon>
      </button>
    }
  </div>

  <!-- Current Language Indicator -->
  @if (showLanguageSelector) {
    <div class="language-indicator">
      <span class="current-language">
        {{ getLanguageDisplayName(getCurrentLanguage()) }}
      </span>
      @if (availableLanguages.length > 1) {
        <span class="language-count">
          ({{ currentLanguageIndex + 1 }}/{{ availableLanguages.length }})
        </span>
      }
    </div>
  }

  <!-- Validation Error Messages -->
  @if (getCurrentLanguageControl().invalid && getCurrentLanguageControl().touched) {
    <div class="error-messages">
      @if (getCurrentLanguageControl().hasError('required')) {
        <span class="error-message">
          {{ field.label || field.fieldName }} is required
        </span>
      }
      @if (getCurrentLanguageControl().hasError('minlength')) {
        <span class="error-message">
          Minimum length is {{ getCurrentLanguageControl().getError('minlength').requiredLength }} characters
        </span>
      }
      @if (getCurrentLanguageControl().hasError('maxlength')) {
        <span class="error-message">
          Maximum length is {{ getCurrentLanguageControl().getError('maxlength').requiredLength }} characters
        </span>
      }
      @if (getCurrentLanguageControl().hasError('pattern')) {
        <span class="error-message">
          Invalid format
        </span>
      }
    </div>
  }

  <!-- Debug Info (only in development) -->
  @if (false) {
    <div class="debug-info">
      <small>
        Available Languages: {{ availableLanguages.join(', ') }}<br>
        Current: {{ getCurrentLanguage() }}<br>
        Value: {{ getCurrentLanguageControl().value }}
      </small>
    </div>
  }

</div>
