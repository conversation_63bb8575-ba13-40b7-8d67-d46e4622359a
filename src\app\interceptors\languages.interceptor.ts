import { HttpInterceptorFn } from '@angular/common/http';

export const languagesInterceptor: HttpInterceptorFn = (req, next) => {
  // Get user languages from localStorage
  const userLanguages = getUserLanguagesFromStorage();

  // Only add languages header if languages exist
  if (userLanguages && userLanguages.length > 0) {
    const modifiedReq = req.clone({
      setHeaders: {
        'Accept-Language': userLanguages.join(','),
        'X-User-Languages': JSON.stringify(userLanguages)
      }
    });
    return next(modifiedReq);
  }

  return next(req);
};

/**
 * Helper function to get user languages from localStorage
 */
function getUserLanguagesFromStorage(): string[] {
  try {
    const languages = localStorage.getItem('userLanguages');
    return languages ? JSON.parse(languages) : [];
  } catch (error) {
    console.warn('Error parsing user languages from localStorage:', error);
    return [];
  }
}
